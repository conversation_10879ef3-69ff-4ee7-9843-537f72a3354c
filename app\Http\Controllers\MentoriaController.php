<?php

namespace App\Http\Controllers;

use App\Models\Mentoria;
use App\Models\Paciente;
use App\Models\Dentista;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Utils;
use App\Models\MetaTerapeutica;

class MentoriaController extends Controller
{
    public function getAll(Request $request)
    {
        try {
            $user = auth()->payload();

            // Se for system_admin, retorna todas as mentorias
            // Se não for, retorna apenas as mentorias onde o usuário é o solicitante
            if ($user['system_admin']) {
                $mentorias = Mentoria::with(['paciente', 'paciente.clinica', 'solicitante'])->get();
            } else {
                // Buscar o dentista_id do usuário logado
                // $dentista_id = $user['dentista']['id'] ?? null;

                // if (!$dentista_id) {
                //     return responseError(['message' => 'Usuário não possui dentista associado']);
                // }

                $mentorias = Mentoria::with(['paciente', 'paciente.clinica', 'solicitante'])
                    ->where('solicitante_id', auth()->user()->id)
                    ->get();
            }

            $response = responseSuccess(['data' => $mentorias]);
        } catch (\Exception $e) {
            // Log::error($e->getMessage());
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function solicitarMentoria(Request $request)
    {
        $body = $request->all();

        try {
            DB::transaction(function () use ($body) {
                // Delete existing analyses for the paciente_id
                Mentoria::where('paciente_id', $body['paciente_id'])->delete();

                $mentoria = new Mentoria();
                $mentoria->paciente_id = $body['paciente_id'];
                $mentoria->solicitante_id = auth()->user()->id;
                $mentoria->observacao = $body['observacao'];
                $mentoria->save();
            });

            $response = responseSuccess();
        } catch (\Exception $e) {
            // Log::error($e->getMessage());
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }
}
